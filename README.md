# 🌙 深灰背景终端主题配置

专为深灰色背景终端设计的优化配置，提供舒适的护眼配色方案和丰富的开发环境信息显示。

## ✨ 特性

### 🎨 深灰背景优化提示符
- **护眼配色**：专为深灰色背景优化，避免使用纯黑色文字
- **高对比度**：使用粗体颜色确保清晰可见
- **双行设计**：信息丰富且结构清晰
- **智能缓存**：性能优化，响应迅速

### 🔍 环境信息
- **quickenv**：快速环境概览，显示Python、Git、Node.js等信息
- **fullenv**：完整环境详情，包含系统资源和开发工具
- **envswitch**：环境切换助手，帮助管理Python环境
- **initproject**：项目环境初始化，一键设置Git和Python环境
- **智能检测**：自动识别pyenv、conda、pipenv、Node.js、Go、Rust

### ⚡ 实用别名
- **文件操作**：`ll`, `la`, `..`, `...`
- **Python开发**：`py`, `pyversion`, `pypath`, `pyinfo`, `mkvenv`, `activate`, `lsvenv`
- **环境管理**：`pyenvlist`, `condalist`, `condaactivate`（如果安装）
- **Git操作**：`gs`, `ga`, `gc`, `gp`, `gl`
- **系统信息**：`sysinfo`, `diskinfo`, `myip`

### 🧠 智能功能
- **Git集成**：自动显示分支和状态，带缓存优化
- **多环境支持**：自动识别Python venv、conda、pyenv、pipenv
- **开发环境检测**：智能识别Node.js、Go、Rust项目环境
- **自动补全**：命令、文件名、Git分支
- **性能优化**：智能缓存，异步检查，无延迟体验

## 🚀 快速开始

### 1. 安装

```bash
# 克隆或下载项目
git clone <repository-url>
cd 终端命令行优化

# 运行安装脚本
chmod +x install.sh
./install.sh
```

### 2. 手动安装

```bash
# 添加到 ~/.zshrc
echo "source $(pwd)/terminal_config.zsh" >> ~/.zshrc

# 重新加载配置
source ~/.zshrc
```

### 3. 演示

```bash
# 运行演示脚本
chmod +x demo.sh
./demo.sh
```

## 📋 使用说明

### 环境信息命令

#### quickenv - 快速环境概览
```bash
$ quickenv
⚡ 快速环境信息
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🖥️  系统: Darwin 25.0.0 (arm64)
📁 目录: ~/Documents/myproject
🐍 Python: 3.12.11 (pyenv)
   路径: /Users/<USER>/.pyenv/versions/3.12.11/bin/python3
🔧 pyenv: 3.12.11
🔧 虚拟环境: myproject
⚡ Node.js: v23.7.0
📝 Git: 2.39.5
🌿 分支: main
   状态: 工作区干净
🐚 Shell: Zsh 5.9
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

#### envswitch - 环境切换助手
```bash
$ envswitch
🔄 环境切换助手
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
当前环境:
   🐍 Python: 3.12.11 (pyenv)

可用操作:
pyenv版本管理:
   pyenvlist    - 查看所有Python版本
   pyenvglobal  - 设置全局Python版本
   pyenvlocal   - 设置本地Python版本

虚拟环境管理:
   mkvenv <name>  - 创建虚拟环境
   activate       - 激活虚拟环境
   deactivate     - 退出虚拟环境
   lsvenv         - 列出虚拟环境
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

#### initproject - 项目环境初始化
```bash
$ initproject myproject
🚀 项目环境初始化: myproject
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
初始化Git仓库...
✅ Git仓库已初始化
创建Python虚拟环境...
✅ 虚拟环境已创建
激活虚拟环境...
✅ 虚拟环境已激活
升级pip...
✅ pip已升级
创建requirements.txt...
✅ requirements.txt已创建

🎉 项目环境初始化完成！
下一步:
   1. 激活虚拟环境: source .venv/bin/activate
   2. 安装依赖: pip install -r requirements.txt
   3. 开始开发！
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

#### fullenv - 完整环境详情
```bash
$ fullenv
🌟 完整环境信息
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💻 系统信息:
   操作系统: Darwin
   内核版本: 22.6.0
   架构: x86_64
   主机名: MacBook-Pro
   用户名: jiewang

📂 目录信息:
   当前目录: ~/Documents/myproject
   家目录: /Users/<USER>

🐍 Python环境:
   版本: 3.11.5
   路径: /usr/local/bin/python3
   🔧 虚拟环境: myproject
   虚拟环境路径: ~/venvs/myproject

🛠️  开发工具:
   Git: 2.39.3
   Node.js: v18.17.1
   npm: 9.6.7
   Docker: 24.0.5

📊 系统资源:
   CPU: Apple M2 Pro
   内存: 32GB
   磁盘使用: /dev/disk1: 200GB/500GB (40% 已使用)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### 实用别名

#### 文件操作
```bash
ll      # ls -alF (详细列表)
la      # ls -A (显示隐藏文件)
l       # ls -CF (简洁列表)
..      # cd .. (返回上级)
...     # cd ../.. (返回两级)
```

#### Python开发
```bash
py              # python3
pip             # pip3
pyversion       # 显示Python版本详情
pypath          # 显示Python路径
pyinfo          # 显示Python详细信息

# 虚拟环境管理
mkvenv myproject  # 创建虚拟环境
activate          # 激活虚拟环境
deactivate        # 退出虚拟环境
lsvenv            # 列出虚拟环境

# pyenv管理（如果安装）
pyenvlist       # 查看所有Python版本
pyenvglobal     # 设置全局Python版本
pyenvlocal      # 设置本地Python版本

# conda管理（如果安装）
condalist       # 查看所有conda环境
condaactivate   # 激活conda环境
condadeactivate # 退出conda环境
```

#### Git操作
```bash
gs  # git status
ga  # git add
gc  # git commit
gp  # git push
gl  # git log --oneline -10
gd  # git diff
gb  # git branch
gco # git checkout
```

#### 系统信息
```bash
sysinfo  # 系统信息
diskinfo # 磁盘使用
meminfo  # 内存信息 (macOS)
cpuinfo  # CPU信息
myip     # 公网IP
```

## 🎯 提示符示例

### 基本提示符（pyenv + 虚拟环境）
```
╭─👤 jiewang @MacBook-Pro 📁 ~/Documents/myproject 🌿 main 🔧 myproject ⚡ Node v23.7.0
╰─🐍 Python 3.12.11 (pyenv) ⏰ 14:30:45
❯
```

### 有Git修改时
```
╭─👤 jiewang @MacBook-Pro 📁 ~/Documents/myproject 🌿 main* 🔧 myproject
╰─🐍 Python 3.12.11 (pyenv) ⏰ 14:31:12
❯
```

### conda环境
```
╭─👤 jiewang @MacBook-Pro 📁 ~/Documents/myproject 🌿 main 🅒 data-science
╰─🐍 Python 3.11.5 (conda) ⏰ 14:31:45
❯
```

### 多开发环境
```
╭─👤 jiewang @MacBook-Pro 📁 ~/Documents/myproject 🌿 main 🔧 myproject ⚡ Node v23.7.0 🐹 Go 1.21.5
╰─🐍 Python 3.12.11 (pyenv+venv) ⏰ 14:32:10
❯
```

## 🔧 安装选项

### 完整安装
```bash
./install.sh
```

### 仅备份配置
```bash
./install.sh --backup
```

### 仅测试配置
```bash
./install.sh --test
```

### 显示帮助
```bash
./install.sh --help
```

## 📦 依赖要求

### 必需
- **zsh**: 5.0或更高版本
- **bash**: 用于安装脚本

### 推荐
- **Python 3.x**: 用于Python相关功能
- **Git**: 用于Git集成
- **Nerd Fonts**: 用于图标显示

### 可选
- **pyenv**: Python版本管理，支持多版本切换
- **conda**: Conda环境管理，数据科学开发
- **pipenv**: 现代Python包管理工具
- **Node.js**: JavaScript/TypeScript开发环境
- **Go**: Go语言开发环境
- **Rust**: Rust语言开发环境

## 🎨 字体配置

### 推荐字体
- **Hack Nerd Font**: 等宽字体，包含所有图标
- **FiraCode Nerd Font**: 编程字体，支持连字
- **JetBrains Mono Nerd Font**: JetBrains官方字体

### 安装方法

#### macOS (使用Homebrew)
```bash
brew tap homebrew/cask-fonts
brew install --cask font-hack-nerd-font
```

#### 手动安装
1. 访问 [Nerd Fonts](https://www.nerdfonts.com/)
2. 下载喜欢的字体
3. 安装字体并配置终端使用

## 🔧 故障排除

### 图标显示为方框
**问题**: 图标显示为方框或问号
**解决**: 安装Nerd Fonts字体并配置终端使用

### Python版本显示错误
**问题**: Python版本与实际不符
**解决**: 检查PATH环境变量
```bash
which python3
echo $PATH
```

### Git分支不显示
**问题**: 在Git仓库中不显示分支信息
**解决**: 确保在Git仓库中
```bash
git status
```

### 虚拟环境不显示
**问题**: 激活虚拟环境后不显示
**解决**: 检查虚拟环境是否激活
```bash
echo $VIRTUAL_ENV
echo $CONDA_DEFAULT_ENV
```

### 性能问题
**问题**: 提示符响应慢
**解决**: 检查Git状态检查
```bash
# 临时禁用Git检查
export DISABLE_GIT_PROMPT=1
```

## 📝 自定义配置

### 修改提示符样式
编辑 `terminal_config.zsh` 文件，修改 `PROMPT` 变量：

```bash
# 自定义提示符
PROMPT='${GREEN}%n${RESET}@${BLUE}%m${RESET}:${YELLOW}%~${RESET}${MAGENTA}$(get_git_info)${RESET} $ '
```

### 添加自定义别名
在 `terminal_config.zsh` 末尾添加：

```bash
# 自定义别名
alias myalias='mycommand'
```

### 修改颜色主题
修改颜色变量：

```bash
# 自定义颜色
local RED="%{$fg[red]%}"
local GREEN="%{$fg[green]%}"
local BLUE="%{$fg[blue]%}"
```

## 🔄 更新配置

### 检查更新
```bash
git pull origin main
```

### 重新加载配置
```bash
source ~/.zshrc
```

### 备份当前配置
```bash
./install.sh --backup
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请：
1. 查看 [故障排除](#-故障排除) 部分
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 查看演示: `./demo.sh`

---

**享受全新的终端体验！** 🚀
# 测试修改
