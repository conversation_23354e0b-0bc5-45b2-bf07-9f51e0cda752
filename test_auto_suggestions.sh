#!/bin/bash
# =============================================================================
# 智能自动提示功能测试脚本
# 功能：测试和演示增强的自动提示功能
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 图标
ICON_SUCCESS="✅"
ICON_WARNING="⚠️"
ICON_ERROR="❌"
ICON_INFO="ℹ️"
ICON_ROCKET="🚀"
ICON_MAGIC="🔮"
ICON_KEYBOARD="⌨️"

echo -e "${CYAN}${ICON_MAGIC} 智能自动提示功能测试程序${NC}"
echo -e "${BLUE}======================================${NC}"
echo

# 检查zsh环境
if [[ "$SHELL" != *"zsh"* ]]; then
    echo -e "${ICON_WARNING} ${YELLOW}警告: 当前Shell不是zsh，某些功能可能无法正常工作${NC}"
    echo -e "${WHITE}当前Shell: $SHELL${NC}"
    echo
fi

# 重新加载配置
echo -e "${ICON_INFO} ${BLUE}重新加载终端配置...${NC}"
source ./enhanced_dark_theme.zsh

echo -e "${ICON_SUCCESS} ${GREEN}配置加载完成！${NC}"
echo

# 显示功能说明
echo -e "${ICON_MAGIC} ${PURPLE}智能自动提示功能说明:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo
echo -e "${WHITE}🎯 核心特性:${NC}"
echo -e "  ${GREEN}•${NC} ${CYAN}实时智能建议${NC} - 输入时自动显示建议（蓝色下划线）"
echo -e "  ${GREEN}•${NC} ${CYAN}多源建议${NC} - 历史记录、命令模式、路径补全、环境变量"
echo -e "  ${GREEN}•${NC} ${CYAN}智能缓存${NC} - 1秒缓存提高响应速度"
echo -e "  ${GREEN}•${NC} ${CYAN}灵活接受${NC} - 可接受完整建议、单词或字符"
echo
echo -e "${WHITE}${ICON_KEYBOARD} 快捷键操作:${NC}"
echo -e "  ${YELLOW}Ctrl+F 或 →${NC}     接受完整建议"
echo -e "  ${YELLOW}Alt+F${NC}           接受一个单词"
echo -e "  ${YELLOW}Alt+C${NC}           接受一个字符"
echo -e "  ${YELLOW}Shift+→${NC}         接受单词 (macOS)"
echo -e "  ${YELLOW}Ctrl+→${NC}          接受全部 (macOS)"
echo -e "  ${YELLOW}Ctrl+G${NC}          清除建议"
echo -e "  ${YELLOW}Backspace${NC}       智能退格"
echo
echo -e "${WHITE}🎨 视觉效果:${NC}"
echo -e "  ${BLUE}建议文字显示为蓝色下划线，适配深灰背景${NC}"
echo -e "  ${BLUE}建议会根据输入实时更新${NC}"
echo

# 创建测试历史记录
echo -e "${ICON_INFO} ${BLUE}创建测试历史记录...${NC}"
echo "git status" >> ~/.zsh_history
echo "git add ." >> ~/.zsh_history
echo "git commit -m 'test commit'" >> ~/.zsh_history
echo "cd ~/Documents" >> ~/.zsh_history
echo "ls -la" >> ~/.zsh_history
echo "python -i" >> ~/.zsh_history
echo "brew install node" >> ~/.zsh_history
echo "npm install" >> ~/.zsh_history
echo "docker ps" >> ~/.zsh_history
echo "curl -X GET https://api.example.com" >> ~/.zsh_history

# 重新加载历史
fc -R

echo -e "${ICON_SUCCESS} ${GREEN}测试历史记录已创建${NC}"
echo

# 显示测试建议
echo -e "${ICON_ROCKET} ${PURPLE}开始交互式测试:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo
echo -e "${WHITE}请在新的zsh会话中尝试以下输入，观察自动建议效果:${NC}"
echo
echo -e "${CYAN}1. 历史匹配测试:${NC}"
echo -e "   输入: ${YELLOW}git${NC} (应该建议 'status')"
echo -e "   输入: ${YELLOW}git s${NC} (应该建议 'tatus')"
echo -e "   输入: ${YELLOW}cd ~/D${NC} (应该建议 'ocuments')"
echo
echo -e "${CYAN}2. 命令模式测试:${NC}"
echo -e "   输入: ${YELLOW}ls${NC} (应该建议 ' -la')"
echo -e "   输入: ${YELLOW}python${NC} (应该建议 ' -i')"
echo -e "   输入: ${YELLOW}brew ${NC} (应该建议 'install')"
echo
echo -e "${CYAN}3. 路径补全测试:${NC}"
echo -e "   输入: ${YELLOW}cd /Us${NC} (应该建议路径补全)"
echo -e "   输入: ${YELLOW}cat ~/.z${NC} (应该建议 'shrc')"
echo
echo -e "${CYAN}4. 环境变量测试:${NC}"
echo -e "   输入: ${YELLOW}echo \$HO${NC} (应该建议 'ME')"
echo -e "   输入: ${YELLOW}echo \$PA${NC} (应该建议 'TH')"
echo

# 启动新的zsh会话进行测试
echo -e "${ICON_MAGIC} ${GREEN}启动测试会话...${NC}"
echo -e "${WHITE}在新会话中，你将看到智能自动提示功能！${NC}"
echo -e "${WHITE}输入 'exit' 返回到当前会话${NC}"
echo
echo -e "${YELLOW}按任意键启动测试会话...${NC}"
read -n 1 -s

# 启动新的zsh会话
exec zsh
