#!/bin/zsh
# =============================================================================
# 智能自动提示功能演示脚本
# 功能：演示各种自动提示场景
# =============================================================================

# 加载配置
source ./enhanced_dark_theme.zsh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}🔮 智能自动提示功能演示${NC}"
echo -e "${BLUE}========================${NC}"
echo

# 演示函数
demo_suggestion() {
    local input="$1"
    local description="$2"
    
    echo -e "${WHITE}演示: ${description}${NC}"
    echo -e "${YELLOW}输入: ${input}${NC}"
    
    # 模拟输入并获取建议
    BUFFER="$input"
    _zsh_autosuggest_suggest
    
    if [[ -n "$POSTDISPLAY" ]]; then
        echo -e "${BLUE}建议: ${input}${POSTDISPLAY}${NC}"
    else
        echo -e "${RED}无建议${NC}"
    fi
    echo
    
    # 清除状态
    BUFFER=""
    POSTDISPLAY=""
}

# 创建演示历史
echo "git status" >> ~/.zsh_history
echo "git add ." >> ~/.zsh_history
echo "git commit -m 'update features'" >> ~/.zsh_history
echo "git push origin main" >> ~/.zsh_history
echo "cd ~/Documents/projects" >> ~/.zsh_history
echo "ls -la --color=auto" >> ~/.zsh_history
echo "python -i script.py" >> ~/.zsh_history
echo "pip install requests" >> ~/.zsh_history
echo "brew install node" >> ~/.zsh_history
echo "npm install express" >> ~/.zsh_history
echo "docker run -it ubuntu" >> ~/.zsh_history
echo "curl -X POST -H 'Content-Type: application/json'" >> ~/.zsh_history

# 重新加载历史
fc -R

echo -e "${GREEN}✅ 演示历史记录已创建${NC}"
echo

# 开始演示
echo -e "${PURPLE}🎯 开始功能演示:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo

# 1. 历史匹配演示
echo -e "${CYAN}📚 1. 历史记录匹配演示${NC}"
demo_suggestion "git" "Git命令建议"
demo_suggestion "git s" "Git status建议"
demo_suggestion "git a" "Git add建议"
demo_suggestion "git c" "Git commit建议"
demo_suggestion "cd ~/D" "目录路径建议"
demo_suggestion "python -" "Python参数建议"

# 2. 命令模式演示
echo -e "${CYAN}⚡ 2. 智能命令模式演示${NC}"
demo_suggestion "ls" "ls命令参数建议"
demo_suggestion "brew " "brew子命令建议"
demo_suggestion "pip " "pip子命令建议"
demo_suggestion "docker " "docker子命令建议"
demo_suggestion "npm " "npm子命令建议"

# 3. 路径补全演示
echo -e "${CYAN}📁 3. 路径补全演示${NC}"
demo_suggestion "cd /Us" "系统路径建议"
demo_suggestion "cat ~/.z" "配置文件建议"
demo_suggestion "vim ~/." "隐藏文件建议"

# 4. 环境变量演示
echo -e "${CYAN}🌍 4. 环境变量演示${NC}"
demo_suggestion "echo \$HO" "HOME变量建议"
demo_suggestion "echo \$PA" "PATH变量建议"
demo_suggestion "echo \$US" "USER变量建议"

echo -e "${GREEN}✅ 演示完成！${NC}"
echo
echo -e "${WHITE}💡 使用提示:${NC}"
echo -e "  ${YELLOW}•${NC} 建议以蓝色下划线显示"
echo -e "  ${YELLOW}•${NC} 使用 Ctrl+F 或 → 接受完整建议"
echo -e "  ${YELLOW}•${NC} 使用 Alt+F 接受一个单词"
echo -e "  ${YELLOW}•${NC} 使用 Alt+C 接受一个字符"
echo -e "  ${YELLOW}•${NC} 使用 Ctrl+G 清除建议"
echo
echo -e "${CYAN}🚀 现在开始使用智能自动提示功能吧！${NC}"
