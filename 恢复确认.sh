#!/bin/bash
# =============================================================================
# 配置恢复确认脚本
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

echo -e "${CYAN}🔄 终端配置恢复确认${NC}"
echo -e "${BLUE}==================${NC}"
echo

# 检查当前配置
echo -e "${BLUE}正在检查当前配置...${NC}"
echo

# 检查 .zshrc 文件
if grep -q "Powerlevel10k" ~/.zshrc; then
    echo -e "${GREEN}✅ 检测到 Powerlevel10k 配置${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到 Powerlevel10k 配置${NC}"
fi

if grep -q "Oh My Zsh" ~/.zshrc; then
    echo -e "${GREEN}✅ 检测到 Oh My Zsh 配置${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到 Oh My Zsh 配置${NC}"
fi

# 检查是否还有我们的自定义配置
if grep -q "enhanced_dark_theme.zsh" ~/.zshrc; then
    echo -e "${YELLOW}⚠️  仍然包含自定义主题配置${NC}"
else
    echo -e "${GREEN}✅ 已移除自定义主题配置${NC}"
fi

echo
echo -e "${WHITE}📋 当前 .zshrc 文件前10行:${NC}"
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
head -10 ~/.zshrc
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo

echo -e "${WHITE}📋 备份文件列表:${NC}"
ls -la ~/.zshrc* | tail -5

echo
echo -e "${GREEN}✅ 配置恢复完成！${NC}"
echo
echo -e "${WHITE}🔄 下一步操作:${NC}"
echo -e "  1. ${CYAN}重新启动终端${NC} 或打开新的终端窗口"
echo -e "  2. ${CYAN}验证提示符${NC} 是否恢复到原始状态"
echo -e "  3. 如果使用 Powerlevel10k，应该看到原来的主题"
echo
echo -e "${YELLOW}💡 如果需要重新应用自定义配置:${NC}"
echo -e "  运行: ${CYAN}./install_dark_theme.sh${NC}"
